# 🚀 <PERSON> - Full Stack Developer Portfolio

A modern, interactive portfolio website built with React, TypeScript, and Tailwind CSS. Features a terminal-style interface with smooth animations, 3D elements, and a comprehensive showcase of skills and projects.

## ✨ Features

### 🎯 Core Features
- **Interactive Terminal Interface** - Authentic terminal-style splash screen with command execution
- **Modern Design System** - Clean, professional design with light theme and terminal aesthetics
- **Smooth Animations** - Powered by Framer Motion for fluid user interactions
- **Responsive Layout** - Mobile-first design that works on all devices
- **Performance Optimized** - Lazy loading, code splitting, and optimized assets

### 🛠️ Technical Features
- **TypeScript** - Full type safety and better developer experience
- **Tailwind CSS v4** - Modern utility-first CSS framework
- **Component Library** - Comprehensive UI components based on Radix UI
- **Custom Hooks** - Reusable logic for animations, scroll tracking, and performance
- **Accessible** - WCAG compliant with proper focus management and screen reader support

### 📱 User Experience
- **Terminal Simulation** - Interactive command-line interface for navigation
- **Particle Effects** - Dynamic background animations
- **Smooth Scrolling** - Seamless navigation between sections
- **Touch Optimized** - Gesture-friendly interactions for mobile devices
- **Performance Monitoring** - Adaptive rendering based on device capabilities

## 🚀 Quick Start

### Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (version 18.0 or higher)
- **npm** or **yarn** or **pnpm** (package manager)
- **Git** (for version control)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/vickymosafan/portfolio.git
   cd portfolio
   ```

2. **Install dependencies**
   ```bash
   # Using npm
   npm install

   # Using yarn
   yarn install

   # Using pnpm
   pnpm install
   ```

3. **Start the development server**
   ```bash
   # Using npm
   npm run dev

   # Using yarn
   yarn dev

   # Using pnpm
   pnpm dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000` to see the application running.

## 📦 Dependencies

### Core Dependencies
```json
{
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "typescript": "^5.0.0",
  "tailwindcss": "^4.0.0",
  "motion": "^10.16.0",
  "lucide-react": "^0.263.0"
}
```

### UI & Styling
```json
{
  "clsx": "^2.0.0",
  "tailwind-merge": "^1.14.0",
  "class-variance-authority": "^0.7.0",
  "@radix-ui/react-dialog": "^1.0.0",
  "@radix-ui/react-dropdown-menu": "^2.0.0",
  "@radix-ui/react-slot": "^1.0.0",
  "sonner": "^1.0.0"
}
```

### Development Dependencies
```json
{
  "@types/react": "^18.2.0",
  "@types/react-dom": "^18.2.0",
  "@vitejs/plugin-react": "^4.0.0",
  "vite": "^4.4.0",
  "eslint": "^8.45.0",
  "prettier": "^3.0.0"
}
```

## 🏗️ Project Structure

```
portfolio/
├── 📁 components/           # React components
│   ├── 📁 ui/              # Reusable UI components
│   │   ├── button.tsx      # Button component
│   │   ├── card.tsx        # Card component
│   │   ├── badge.tsx       # Badge component
│   │   └── ...             # Other UI components
│   ├── 📁 figma/           # Figma-specific components
│   ├── 📁 hooks/           # Custom React hooks
│   ├── HeroSection.tsx     # Hero/landing section
│   ├── AboutSection.tsx    # About section
│   ├── SkillsSection.tsx   # Skills showcase
│   ├── ProjectsSection.tsx # Projects gallery
│   ├── ContactSection.tsx  # Contact form
│   ├── Navigation.tsx      # Navigation component
│   ├── SplashScreen.tsx    # Terminal splash screen
│   └── Footer.tsx          # Footer component
├── 📁 lib/                 # Utility libraries
│   ├── 📁 data/            # Static data
│   ├── 📁 hooks/           # Custom hooks
│   ├── config.ts           # App configuration
│   ├── types.ts            # TypeScript types
│   └── utils.ts            # Utility functions
├── 📁 styles/              # Global styles
│   └── globals.css         # Global CSS with Tailwind
├── App.tsx                 # Main app component
└── README.md              # This file
```

## 🎨 Design System

### Color Scheme
The portfolio uses a modern light theme with terminal-style dark components:

```css
:root {
  --background: oklch(0.985 0 0);      /* Light background */
  --foreground: oklch(0.145 0 0);      /* Dark text */
  --primary: oklch(0.145 0 0);         /* Primary color */
  --secondary: oklch(0.9 0 0);         /* Secondary color */
  --accent: oklch(0.9 0 0);            /* Accent color */
  --muted: oklch(0.95 0 0);            /* Muted background */
  --border: oklch(0.89 0 0);           /* Border color */
}
```

### Typography
- **Base font size**: 14px
- **Font weights**: 400 (normal), 500 (medium)
- **Line height**: 1.5 for optimal readability

### Responsive Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🔧 Configuration

### Environment Variables
Create a `.env.local` file in the root directory:

```env
# App Configuration
VITE_APP_NAME="Vicky Mosafan Portfolio"
VITE_APP_URL="https://vickymosafan.dev"

# Contact Form (if using a service like Formspree)
VITE_CONTACT_FORM_ID="your-form-id"

# Analytics (optional)
VITE_GA_TRACKING_ID="G-XXXXXXXXXX"
```

### Customization

#### Personal Information
Update `/lib/config.ts` with your personal information:

```typescript
export const APP_CONFIG = {
  personal: {
    name: 'Your Name',
    username: 'your_username',
    title: 'Your Title',
    location: 'Your Location',
    email: '<EMAIL>',
    // ... other personal info
  },
  // ... other configuration
};
```

#### Portfolio Data
Update `/lib/data/portfolio.ts` with your projects and skills:

```typescript
export const projects = [
  {
    id: 'project-1',
    title: 'Your Project',
    description: 'Project description',
    // ... other project data
  },
  // ... more projects
];
```

## 🚀 Available Scripts

### Development
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Type checking
npm run type-check
```

### Code Quality
```bash
# Run ESLint
npm run lint

# Fix ESLint errors
npm run lint:fix

# Format code with Prettier
npm run format

# Check formatting
npm run format:check
```

### Testing
```bash
# Run tests
npm run test

# Run tests in watch mode
npm run test:watch

# Generate test coverage
npm run test:coverage
```

## 🎯 Key Components

### SplashScreen
Interactive terminal interface that users must navigate through:
- Terminal command execution simulation
- Real-time typing effects
- Server status indicators
- Command history tracking

### HeroSection
Main landing area with:
- Animated terminal window
- Tech stack showcase
- Professional information
- Matrix-style background effects

### Navigation
Vertical navigation sidebar with:
- Icon-based navigation
- Smooth scroll to sections
- Mobile-responsive menu
- Terminal-style interactions

### UI Components
Comprehensive component library including:
- **Buttons** - Various styles and states
- **Cards** - Content containers
- **Badges** - Status indicators
- **Forms** - Input components
- **Dialogs** - Modal components
- **Tooltips** - Contextual help

## 🎨 Animation System

### Framer Motion Integration
The portfolio uses Framer Motion for smooth animations:

```typescript
// Example usage
import { motion } from 'motion/react';

<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.5 }}
>
  Content
</motion.div>
```

### Custom Animation Utilities
Pre-configured animation presets available in `/lib/utils.ts`:

```typescript
// Fade animations
animations.fadeIn(0.2)
animations.fadeInUp(0.3)
animations.fadeInLeft(0.4)

// Scale animations
animations.scaleIn(0.2)

// Hover effects
animations.hover.lift
animations.hover.glow
```

## 📱 Responsive Design

### Mobile-First Approach
The portfolio is built with mobile-first responsive design:

```css
/* Mobile styles by default */
.component {
  padding: 1rem;
}

/* Tablet styles */
@media (min-width: 768px) {
  .component {
    padding: 2rem;
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  .component {
    padding: 3rem;
  }
}
```

### Touch Optimization
- Large touch targets (minimum 44px)
- Gesture-friendly interactions
- Swipe navigation support
- Haptic feedback simulation

## 🔒 Performance Optimization

### Code Splitting
Components are lazy-loaded for optimal performance:

```typescript
// Example lazy loading
const LazyComponent = lazy(() => import('./components/HeavyComponent'));
```

### Image Optimization
- WebP format support
- Lazy loading for images
- Responsive image sizing
- Fallback handling

### Bundle Optimization
- Tree shaking enabled
- Dead code elimination
- Minification in production
- Gzip compression

## 🧪 Testing

### Unit Tests
```bash
# Run unit tests
npm run test:unit

# Watch mode
npm run test:unit:watch
```

### Integration Tests
```bash
# Run integration tests
npm run test:integration
```

### E2E Tests
```bash
# Run end-to-end tests
npm run test:e2e
```

## 🚀 Deployment

### Build for Production
```bash
# Create production build
npm run build

# The build artifacts will be stored in the `dist/` directory
```

### Deployment Platforms

#### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

#### Netlify
```bash
# Install Netlify CLI
npm i -g netlify-cli

# Deploy
netlify deploy --prod --dir=dist
```

#### GitHub Pages
```bash
# Build and deploy to GitHub Pages
npm run deploy
```

## 🔧 Troubleshooting

### Common Issues

#### 1. **Dependencies Installation Errors**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### 2. **TypeScript Errors**
```bash
# Check TypeScript configuration
npm run type-check

# Update TypeScript
npm update typescript
```

#### 3. **Build Failures**
```bash
# Check for build errors
npm run build

# Clear build cache
rm -rf dist .cache
npm run build
```

#### 4. **Animation Performance Issues**
```bash
# Reduce animation complexity in config
# Check browser compatibility
# Monitor performance metrics
```

### Environment Issues

#### Node.js Version
Ensure you're using Node.js 18.0 or higher:
```bash
node --version
# Should output v18.0.0 or higher
```

#### Package Manager Issues
If using multiple package managers, stick to one:
```bash
# Remove other lock files
rm -rf yarn.lock pnpm-lock.yaml
# Keep only package-lock.json if using npm
```

## 📞 Support

### Getting Help
- **Documentation**: Check this README first
- **Issues**: Open an issue on GitHub
- **Discussions**: Join the community discussions
- **Email**: Contact <EMAIL>

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

### License
This project is licensed under the MIT License. See `LICENSE` file for details.

## 🙏 Acknowledgments

### Libraries & Tools
- [React](https://reactjs.org/) - UI library
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [Framer Motion](https://www.framer.com/motion/) - Animations
- [Radix UI](https://www.radix-ui.com/) - UI primitives
- [Lucide React](https://lucide.dev/) - Icons

### Inspiration
- Terminal-style interfaces
- Modern portfolio designs
- Developer-focused aesthetics
- Clean, minimal design principles

---

## 🔄 Changelog

### Version 1.0.0
- Initial release with terminal interface
- Complete portfolio sections
- Responsive design implementation
- Animation system integration
- Performance optimizations

---

**Built with ❤️ by Vicky Mosafan**

*A modern portfolio showcasing full-stack development skills with an interactive terminal experience.*